/* 个人页样式 */
.profile-container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #F5F5F5;
}

/* 用户信息 */
.user-info {
  padding: 40rpx 30rpx;
}

.user-main {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #FF6B35;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  color: #ffffff;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
}

.member-level {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
  margin-top: 8rpx;
}

.user-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 0;
  border-top: 1rpx solid #F0F0F0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B35;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999999;
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #F0F0F0;
}

.member-level {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  color: #ffffff;
  font-weight: bold;
}

.member-level.gold {
  background-color: #FFD700;
}

.member-level.silver {
  background-color: #C0C0C0;
}

.member-level.bronze {
  background-color: #CD7F32;
}

.member-level.normal {
  background-color: #999999;
}

.member-expire {
  font-size: 24rpx;
  color: #999999;
}

.login-btn {
  padding: 15rpx 30rpx;
  background-color: #FF6B35;
  color: #ffffff;
  border-radius: 25rpx;
  font-size: 28rpx;
}

/* 功能菜单 */
.menu-section {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.menu-title {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999999;
}

.benefit-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.benefit-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.benefit-desc {
  font-size: 24rpx;
  color: #999999;
}

.benefit-arrow {
  font-size: 32rpx;
  color: #CCCCCC;
}

/* 订单管理 */
.order-section {
  margin-bottom: 20rpx;
}

.order-tabs {
  display: flex;
  margin-bottom: 30rpx;
  background-color: #F5F5F5;
  border-radius: 10rpx;
  padding: 6rpx;
}

.order-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.order-tab.active {
  background-color: #FF6B35;
  color: #ffffff;
  font-weight: bold;
}

.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background-color: #FFF8F5;
  border-radius: 15rpx;
  padding: 30rpx;
  border-left: 6rpx solid #FF6B35;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.order-number {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.order-status {
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  color: #ffffff;
}

.order-status.pending {
  background-color: #F7931E;
}

.order-status.cooking {
  background-color: #FF6B35;
}

.order-status.completed {
  background-color: #4CAF50;
}

.order-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.order-table {
  font-size: 26rpx;
  color: #666666;
}

.order-time {
  font-size: 24rpx;
  color: #999999;
}

.order-items {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-dishes {
  font-size: 26rpx;
  color: #666666;
  flex: 1;
  margin-right: 20rpx;
}

.order-price {
  font-size: 32rpx;
  font-weight: bold;
}

.pay-button {
  margin-left: 20rpx;
  background-color: #FF6B35 !important;
  color: white !important;
  border-radius: 20rpx;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  height: auto;
  line-height: 1.2;
}

.pay-button::after {
  border: none;
}

/* 设置 */
.settings-section {
  margin-bottom: 20rpx;
}

.settings-list {
  display: flex;
  flex-direction: column;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-text {
  font-size: 32rpx;
  color: #333333;
}

.setting-arrow {
  font-size: 32rpx;
  color: #CCCCCC;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  color: #999999;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-state text {
  font-size: 28rpx;
}

/* 优惠券弹窗 */
.coupon-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.coupon-modal.show {
  opacity: 1;
  visibility: visible;
}

.coupon-content {
  position: absolute;
  top: 20%;
  left: 8%;
  right: 8%;
  max-height: 65%;
  background-color: #ffffff;
  border-radius: 20rpx;
  transform: translateY(-50rpx);
  transition: transform 0.3s ease;
  overflow: hidden;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.coupon-modal.show .coupon-content {
  transform: translateY(0);
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.coupon-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999999;
  border-radius: 50%;
  background-color: #F5F5F5;
}

.coupon-list {
  max-height: 45vh;
  padding: 20rpx 0;
  overflow-y: auto;
}

.coupon-item {
  display: flex;
  background: linear-gradient(135deg, #FF6B35, #F7931E);
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  overflow: hidden;
  color: #ffffff;
  box-sizing: border-box;
  width: calc(100% - 0rpx);
}

.coupon-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 25rpx;
  background-color: rgba(255,255,255,0.1);
  width: 140rpx;
  flex-shrink: 0;
}

.coupon-amount {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.coupon-condition {
  font-size: 24rpx;
  opacity: 0.8;
}

.coupon-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 25rpx 30rpx 25rpx 30rpx;
  min-width: 0;
}

.coupon-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.coupon-expire {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 订单管理弹窗 */
.order-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.order-modal.show {
  opacity: 1;
  visibility: visible;
}

.order-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  max-height: 80vh;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.order-modal.show .order-content {
  transform: translateY(0);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.order-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

/* 邀请记录弹窗 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.invite-modal.show {
  opacity: 1;
  visibility: visible;
}

.invite-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  max-height: 70vh;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.invite-modal.show .invite-content {
  transform: translateY(0);
}

.invite-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.invite-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.invite-list {
  max-height: 50vh;
  padding: 20rpx 30rpx;
}

.invite-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.invite-item:last-child {
  border-bottom: none;
}

.invite-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #FF6B35;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.invite-avatar-text {
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
}

.invite-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.invite-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.invite-time {
  font-size: 24rpx;
  color: #999999;
}
