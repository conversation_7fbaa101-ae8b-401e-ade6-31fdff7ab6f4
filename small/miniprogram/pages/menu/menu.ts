// 菜单页逻辑
interface Dish {
  id: number;
  name: string;
  description: string;
  price: number;
  emoji: string;
  image: string;
  categoryId: number;
  isRecommended?: boolean;
  count?: number;
}

interface Category {
  id: number;
  name: string;
}

Page({
  data: {
    tableNumber: '', // 桌号
    currentCategory: 1, // 当前分类
    showCartModal: false, // 购物车弹窗显示状态
    
    // 分类数据
    categories: [
      { id: 1, name: '热菜' },
      { id: 2, name: '凉菜' },
      { id: 3, name: '汤品' },
      { id: 4, name: '川菜' },
      { id: 5, name: '素食' },
      { id: 6, name: '海鲜' },
      { id: 7, name: '烧烤' },
      { id: 8, name: '主食' },
      { id: 9, name: '饮品' },
      { id: 10, name: '甜品' }
    ] as Category[],
    
    // 菜品数据
    dishes: [
      // 热菜
      {
        id: 1,
        name: '宫保鸡丁',
        description: '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣',
        price: 28,
        emoji: '🍗',
        image: '/images/dishes/gongbao.jpg',
        categoryId: 1,
        isRecommended: true,
        count: 0
      },
      {
        id: 2,
        name: '麻婆豆腐',
        description: '嫩滑豆腐配麻辣肉末，口感丰富层次分明',
        price: 18,
        emoji: '🥘',
        image: '/images/dishes/mapo.jpg',
        categoryId: 1,
        count: 0
      },
      {
        id: 3,
        name: '红烧肉',
        description: '肥瘦相间，入口即化，甜而不腻',
        price: 35,
        emoji: '🥩',
        image: '/images/dishes/hongshao.jpg',
        categoryId: 1,
        count: 0
      },
      {
        id: 4,
        name: '水煮鱼',
        description: '鲜嫩鱼片，麻辣鲜香，川菜经典',
        price: 45,
        emoji: '🐟',
        image: '/images/dishes/shuizhu.jpg',
        categoryId: 1,
        isRecommended: true,
        count: 0
      },
      {
        id: 5,
        name: '口水鸡',
        description: '鸡肉嫩滑，调料丰富，口感层次分明',
        price: 32,
        emoji: '🐔',
        image: '/images/dishes/koushuiji.jpg',
        categoryId: 1,
        count: 0
      },
      // 凉菜
      {
        id: 6,
        name: '酸辣土豆丝',
        description: '爽脆土豆丝，酸辣开胃，清爽不腻',
        price: 12,
        emoji: '🥔',
        image: '/images/dishes/tudousi.jpg',
        categoryId: 2,
        count: 0
      },
      {
        id: 7,
        name: '凉拌黄瓜',
        description: '清脆黄瓜配蒜泥，清香爽口',
        price: 8,
        emoji: '🥒',
        image: '/images/dishes/huangua.jpg',
        categoryId: 2,
        count: 0
      },
      {
        id: 8,
        name: '夫妻肺片',
        description: '经典川菜凉菜，麻辣鲜香',
        price: 25,
        emoji: '🥓',
        image: '/images/dishes/fuqifeipian.jpg',
        categoryId: 2,
        isRecommended: true,
        count: 0
      },
      // 汤品
      {
        id: 9,
        name: '西红柿鸡蛋汤',
        description: '酸甜西红柿配嫩滑鸡蛋，营养丰富',
        price: 15,
        emoji: '🍅',
        image: '/images/dishes/xihongshi.jpg',
        categoryId: 3,
        count: 0
      },
      {
        id: 10,
        name: '冬瓜排骨汤',
        description: '清淡营养，排骨鲜美，冬瓜清香',
        price: 22,
        emoji: '🍲',
        image: '/images/dishes/dongguatang.jpg',
        categoryId: 3,
        count: 0
      },
      // 饮品
      {
        id: 11,
        name: '鲜榨橙汁',
        description: '新鲜橙子现榨，维C丰富，酸甜可口',
        price: 18,
        emoji: '🍊',
        image: '/images/dishes/orange.jpg',
        categoryId: 9,
        count: 0
      },
      {
        id: 12,
        name: '柠檬蜂蜜茶',
        description: '清香柠檬配天然蜂蜜，清热解腻',
        price: 15,
        emoji: '🍋',
        image: '/images/dishes/lemon.jpg',
        categoryId: 9,
        count: 0
      },
      {
        id: 13,
        name: '酸梅汤',
        description: '传统饮品，酸甜解腻，生津止渴',
        price: 12,
        emoji: '🥤',
        image: '/images/dishes/suanmei.jpg',
        categoryId: 9,
        count: 0
      },
      // 主食
      {
        id: 14,
        name: '蒸蛋羹',
        description: '嫩滑如丝的蒸蛋，营养美味',
        price: 10,
        emoji: '🥚',
        image: '/images/dishes/zhengdan.jpg',
        categoryId: 8,
        count: 0
      },
      {
        id: 15,
        name: '白米饭',
        description: '优质大米蒸制，粒粒饱满',
        price: 3,
        emoji: '🍚',
        image: '/images/dishes/rice.jpg',
        categoryId: 8,
        count: 0
      },
      {
        id: 16,
        name: '担担面',
        description: '四川特色面条，麻辣鲜香',
        price: 16,
        emoji: '🍜',
        image: '/images/dishes/dandanmian.jpg',
        categoryId: 8,
        count: 0
      }
    ] as Dish[],
    
    currentDishes: [] as Dish[], // 当前分类的菜品
    cartItems: [] as Dish[], // 购物车商品
    totalCount: 0, // 购物车总数量
    totalPrice: 0 // 购物车总价格
  },

  onLoad() {
    // 获取桌号
    const tableNumber = wx.getStorageSync('tableNumber') || '未选择'
    this.setData({
      tableNumber: tableNumber
    })

    // 初始化当前分类菜品
    this.updateCurrentDishes()

    // 从本地存储恢复购物车
    this.loadCartFromStorage()
  },

  // 更新当前分类菜品
  updateCurrentDishes() {
    const { dishes, currentCategory } = this.data
    const currentDishes = dishes.filter(dish => dish.categoryId === currentCategory)
    this.setData({
      currentDishes: currentDishes
    })
  },

  // 切换分类
  switchCategory(e: any) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({
      currentCategory: categoryId
    })
    this.updateCurrentDishes()
  },

  // 增加菜品
  increaseDish(e: any) {
    const dishId = e.currentTarget.dataset.id
    const { dishes } = this.data
    
    const updatedDishes = dishes.map(dish => {
      if (dish.id === dishId) {
        return { ...dish, count: (dish.count || 0) + 1 }
      }
      return dish
    })
    
    this.setData({
      dishes: updatedDishes
    })
    
    this.updateCurrentDishes()
    this.updateCart()
  },

  // 减少菜品
  decreaseDish(e: any) {
    const dishId = e.currentTarget.dataset.id
    const { dishes } = this.data
    
    const updatedDishes = dishes.map(dish => {
      if (dish.id === dishId && (dish.count || 0) > 0) {
        return { ...dish, count: dish.count! - 1 }
      }
      return dish
    })
    
    this.setData({
      dishes: updatedDishes
    })
    
    this.updateCurrentDishes()
    this.updateCart()
  },

  // 更新购物车
  updateCart() {
    const { dishes } = this.data
    const cartItems = dishes.filter(dish => (dish.count || 0) > 0)
    const totalCount = cartItems.reduce((sum, item) => sum + (item.count || 0), 0)
    const totalPrice = cartItems.reduce((sum, item) => sum + item.price * (item.count || 0), 0)
    
    this.setData({
      cartItems: cartItems,
      totalCount: totalCount,
      totalPrice: totalPrice
    })
    
    // 保存到本地存储
    wx.setStorageSync('cartItems', cartItems)
  },

  // 从本地存储加载购物车
  loadCartFromStorage() {
    const savedCartItems = wx.getStorageSync('cartItems') || []
    if (savedCartItems.length > 0) {
      const { dishes } = this.data
      const updatedDishes = dishes.map(dish => {
        const savedItem = savedCartItems.find((item: Dish) => item.id === dish.id)
        if (savedItem) {
          return { ...dish, count: savedItem.count }
        }
        return dish
      })
      
      this.setData({
        dishes: updatedDishes
      })
      
      this.updateCurrentDishes()
      this.updateCart()
    }
  },

  // 显示/隐藏购物车弹窗
  toggleCart() {
    this.setData({
      showCartModal: !this.data.showCartModal
    })
  },

  // 隐藏购物车弹窗
  hideCartModal() {
    this.setData({
      showCartModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 清空购物车
  clearCart() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空购物车吗？',
      success: (res) => {
        if (res.confirm) {
          const { dishes } = this.data
          const updatedDishes = dishes.map(dish => ({ ...dish, count: 0 }))
          
          this.setData({
            dishes: updatedDishes,
            showCartModal: false
          })
          
          this.updateCurrentDishes()
          this.updateCart()
          
          wx.showToast({
            title: '购物车已清空',
            icon: 'success'
          })
        }
      }
    })
  },

  // 去结算
  checkout() {
    const { cartItems, totalPrice, totalCount, tableNumber } = this.data

    if (cartItems.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'error'
      })
      return
    }

    // 准备订单数据
    const orderData = {
      tableNumber: tableNumber,
      items: cartItems,
      totalPrice: totalPrice,
      totalCount: totalCount
    }

    // 保存待处理订单数据
    wx.setStorageSync('pendingOrder', orderData)

    // 清空购物车
    const { dishes } = this.data
    const updatedDishes = dishes.map(dish => ({ ...dish, count: 0 }))

    this.setData({
      dishes: updatedDishes,
      showCartModal: false
    })

    this.updateCurrentDishes()
    this.updateCart()

    // 清空本地购物车存储
    wx.removeStorageSync('cartItems')

    // 跳转到订单确认页
    wx.navigateTo({
      url: '/pages/order-confirm/order-confirm'
    })
  }
})
