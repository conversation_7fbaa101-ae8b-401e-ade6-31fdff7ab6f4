/* 菜单页样式 */
.menu-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
}

/* 桌号信息 */
.table-info {
  background-color: #FF6B35;
  color: #ffffff;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 120rpx); /* 减去桌号信息和底部导航的高度 */
}

/* 分类侧边栏 */
.category-sidebar {
  width: 200rpx;
  background-color: #F8F8F8;
  border-right: 1rpx solid #E5E5E5;
  display: flex;
  flex-direction: column;
}

.category-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background-color: #F8F8F8;
  transition: all 0.3s ease;
  flex: 1;
}

.category-item.active {
  background-color: #FFFFFF;
  border-right: 4rpx solid #FF6B35;
}

.category-name {
  font-size: 32rpx;
  color: #666666;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.category-item.active .category-name {
  color: #FF6B35;
  font-weight: bold;
}

/* 菜品容器 */
.dishes-container {
  flex: 1;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* 菜品列表 */
.dishes-scroll {
  flex: 1;
  padding: 0 20rpx;
  height: 100%;
}

.dishes-list {
  padding-bottom: 200rpx; /* 为购物车浮层留出空间 */
}

.dish-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.dish-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
  margin-right: 30rpx;
  background-color: #FFF8F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dish-emoji {
  font-size: 80rpx;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.dish-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.recommend-tag {
  background-color: #FF6B35;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.dish-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.dish-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-price {
  font-size: 32rpx;
}

/* 菜品控制按钮 */
.dish-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  background-color: #FF6B35;
}

.control-btn.disabled {
  background-color: #CCCCCC;
  color: #999999;
}

.control-btn.minus {
  margin-right: 20rpx;
}

.control-btn.plus {
  margin-left: 20rpx;
}

.dish-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  min-width: 40rpx;
  text-align: center;
}

/* 购物车浮层 */
.cart-float {
  position: fixed;
  bottom: 0; /* 紧贴底部 */
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #333333;
  border-radius: 0; /* 去掉圆角，贴合底部 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  transform: translateY(120rpx); /* 隐藏时完全移出屏幕 */
  transition: transform 0.3s ease;
  z-index: 1000; /* 确保在tabbar之上 */
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.cart-float.show {
  transform: translateY(0);
}

.cart-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.cart-icon-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  font-size: 50rpx;
  color: #ffffff;
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #FF6B35;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 30rpx;
  text-align: center;
}

.cart-text {
  display: flex;
  flex-direction: column;
}

.cart-count {
  font-size: 24rpx;
  color: #ffffff;
}

.cart-price {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

.checkout-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 购物车弹窗 */
.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cart-modal.show {
  opacity: 1;
  visibility: visible;
}

.cart-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.cart-modal.show .cart-content {
  transform: translateY(0);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.cart-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.clear-cart {
  font-size: 28rpx;
  color: #FF6B35;
}

.cart-list {
  max-height: 60vh;
  padding: 20rpx 30rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #F0F0F0;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background-color: #FFF8F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cart-item-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.cart-item-price {
  font-size: 24rpx;
}

.cart-item-controls {
  display: flex;
  align-items: center;
}

.cart-item-count {
  font-size: 28rpx;
  color: #333333;
  margin: 0 15rpx;
  min-width: 30rpx;
  text-align: center;
}
