/* 菜单页样式 - 高端餐饮风格 */
.menu-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #FEFEFE; /* 奶白色背景 */
}

/* 桌号信息 - 优雅设计 */
.table-info {
  background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%); /* 绿色渐变 */
  color: #FFFFFF;
  text-align: center;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 12rpx rgba(39, 174, 96, 0.2);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 120rpx); /* 减去桌号信息和底部导航的高度 */
}

/* 分类侧边栏 - 现代设计 */
.category-sidebar {
  width: 200rpx;
  background-color: #F8F9FA; /* 更柔和的背景 */
  border-right: 1rpx solid #ECF0F1; /* 柔和边框 */
  display: flex;
  flex-direction: column;
}

.category-list {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background-color: #F8F8F8;
  transition: all 0.3s ease;
  flex: 1;
}

.category-item.active {
  background-color: #FFFFFF;
  border-right: 4rpx solid #27AE60; /* 主色调 */
}

.category-name {
  font-size: 30rpx; /* 稍微减小字体 */
  color: #7F8C8D; /* 柔和灰色 */
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

.category-item.active .category-name {
  color: #27AE60; /* 主色调 */
  font-weight: 600;
}

/* 菜品容器 */
.dishes-container {
  flex: 1;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

/* 菜品列表 */
.dishes-scroll {
  flex: 1;
  padding: 0 20rpx;
  height: 100%;
}

.dishes-list {
  padding-bottom: 200rpx; /* 为购物车浮层留出空间 */
}

.dish-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.dish-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
  margin-right: 30rpx;
  background-color: #FFF8F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dish-emoji {
  font-size: 80rpx;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.dish-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  flex: 1;
}

.recommend-tag {
  background-color: #F1C40F; /* 金色标签 */
  color: #2C3E50; /* 深色文字 */
  font-size: 20rpx;
  padding: 6rpx 12rpx; /* 增加内边距 */
  border-radius: 12rpx; /* 统一圆角 */
  margin-left: 12rpx;
  font-weight: 500;
}

.dish-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.dish-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-price {
  font-size: 32rpx;
}

/* 菜品控制按钮 - 现代设计 */
.dish-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  width: 56rpx; /* 稍微减小尺寸 */
  height: 56rpx;
  border-radius: 8rpx; /* 现代圆角 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx; /* 减小字体 */
  font-weight: 500;
  color: #FFFFFF;
  background-color: #27AE60; /* 主色调 */
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(39, 174, 96, 0.3);
}

.control-btn:active {
  background-color: #229954;
  transform: scale(0.95);
}

.control-btn.disabled {
  background-color: #BDC3C7; /* 柔和的禁用状态 */
  color: #7F8C8D;
  box-shadow: none;
}

.control-btn.minus {
  margin-right: 16rpx; /* 减少间距 */
}

.control-btn.plus {
  margin-left: 16rpx;
}

.dish-count {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  min-width: 40rpx;
  text-align: center;
}

/* 购物车浮层 */
.cart-float {
  position: fixed;
  bottom: 0; /* 紧贴底部 */
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #333333;
  border-radius: 0; /* 去掉圆角，贴合底部 */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  transform: translateY(120rpx); /* 隐藏时完全移出屏幕 */
  transition: transform 0.3s ease;
  z-index: 1000; /* 确保在tabbar之上 */
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1); /* 添加阴影 */
}

.cart-float.show {
  transform: translateY(0);
}

.cart-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.cart-icon-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  font-size: 50rpx;
  color: #ffffff;
}

.cart-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #F1C40F; /* 金色徽章 */
  color: #2C3E50; /* 深色文字 */
  font-size: 20rpx;
  font-weight: 500;
  padding: 6rpx 10rpx;
  border-radius: 12rpx; /* 统一圆角 */
  min-width: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(241, 196, 15, 0.3);
}

.cart-text {
  display: flex;
  flex-direction: column;
}

.cart-count {
  font-size: 24rpx;
  color: #ffffff;
}

.cart-price {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

.checkout-btn {
  width: 200rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 购物车弹窗 */
.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.cart-modal.show {
  opacity: 1;
  visibility: visible;
}

.cart-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 65vh; /* 进一步减少高度 */
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0; /* 顶部圆角 */
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column; /* 确保内容垂直排列 */
  /* 移除左右边距，让弹窗贴合屏幕 */
}

.cart-modal.show .cart-content {
  transform: translateY(0);
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx; /* 优化内边距 */
  border-bottom: 1rpx solid #ECF0F1; /* 柔和边框 */
  flex-shrink: 0; /* 防止压缩 */
}

.cart-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2C3E50; /* 深色文字 */
}

.clear-cart {
  font-size: 28rpx;
  color: #27AE60; /* 主色调 */
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  background-color: #F8FDF8; /* 淡绿色背景 */
}

.cart-list {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 允许滚动 */
  padding: 16rpx 32rpx 16rpx; /* 减少底部内边距 */
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0; /* 增加间距 */
  border-bottom: 1rpx solid #ECF0F1; /* 柔和边框 */
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-image {
  width: 80rpx; /* 减小尺寸节省空间 */
  height: 80rpx;
  border-radius: 12rpx; /* 统一圆角 */
  margin-right: 20rpx;
  background-color: #F8FDF8; /* 淡绿色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止压缩 */
}

.cart-item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许文字截断 */
}

.cart-item-name {
  font-size: 28rpx;
  color: #2C3E50; /* 深色文字 */
  margin-bottom: 8rpx;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 防止文字换行 */
}

.cart-item-price {
  font-size: 24rpx;
  color: #27AE60; /* 主色调 */
  font-weight: 500;
}

.cart-item-controls {
  display: flex;
  align-items: center;
  flex-shrink: 0; /* 防止压缩 */
}

.cart-item-count {
  font-size: 26rpx; /* 稍微减小字体 */
  color: #2C3E50; /* 深色文字 */
  margin: 0 12rpx; /* 减少间距 */
  min-width: 32rpx;
  text-align: center;
  font-weight: 500;
}

/* 购物车底部结算区域 */
.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #ECF0F1; /* 顶部分割线 */
  background-color: #F8F9FA; /* 浅色背景区分 */
  flex-shrink: 0; /* 防止压缩 */
}

.cart-total {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.total-text {
  font-size: 24rpx;
  color: #7F8C8D; /* 柔和灰色 */
  margin-bottom: 4rpx;
}

.total-price {
  font-size: 32rpx;
  color: #27AE60; /* 主色调 */
  font-weight: 600;
}

.cart-checkout-btn {
  background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%); /* 绿色渐变 */
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 500;
  padding: 16rpx 32rpx;
  border-radius: 8rpx; /* 现代圆角 */
  box-shadow: 0 4rpx 16rpx rgba(39, 174, 96, 0.3);
  transition: all 0.3s ease;
}

.cart-checkout-btn:active {
  background: linear-gradient(135deg, #229954 0%, #27AE60 100%);
  transform: translateY(1rpx);
}
