/* 首页样式 */
.home-container {
  padding: 20rpx;
  min-height: 100vh;
  padding-bottom: 160rpx; /* 为自定义底部导航栏留出空间 */
}



/* 桌号选择 */
.table-selection {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  margin-bottom: 30rpx;
}

.table-options {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.table-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 20rpx;
  background-color: #FFF8F5;
  width: 200rpx;
}

.table-option:active {
  background-color: #FFE8D6;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.icon-text {
  font-size: 60rpx;
}

.option-text {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 桌号输入 */
.table-input {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}

.input-label {
  font-size: 32rpx;
  color: #333333;
  margin-right: 20rpx;
  font-weight: bold;
}

/* 桌号网格 */
.table-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25rpx;
  margin-bottom: 40rpx;
  padding: 0 10rpx;
}

.table-item {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #E5E5E5;
  border-radius: 15rpx;
  background-color: #FFFFFF;
  transition: all 0.3s ease;
  position: relative;
  padding: 15rpx;
  min-height: 100rpx;
  box-sizing: border-box;
}

.table-item.selected {
  border-color: #FF6B35;
  background-color: #FFF8F5;
}

.table-item.occupied {
  background-color: #F5F5F5;
  border-color: #CCCCCC;
  opacity: 0.6;
}

.table-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.table-item.selected .table-number {
  color: #FF6B35;
}

.table-item.occupied .table-number {
  color: #999999;
}

.table-status {
  font-size: 20rpx;
  color: #999999;
  margin-top: 5rpx;
}

/* 手动输入区域 */
.manual-input-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #F8F8F8;
  border-radius: 15rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666666;
  white-space: nowrap;
}

.table-number-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  border: 2rpx solid #E5E5E5;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #FFFFFF;
}

.confirm-btn {
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 30rpx;
  background-color: #FF6B35;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
}

/* 开始点餐按钮 */
.start-order-btn {
  width: 500rpx;
  height: 100rpx;
  line-height: 100rpx;
  font-size: 36rpx;
  margin: 30rpx auto 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-order-btn[disabled] {
  background-color: #CCCCCC !important;
  color: #999999 !important;
}

/* 店铺详情 */
.shop-details {
  padding: 30rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
  width: 50rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #666666;
}

/* 今日推荐 */
.today-recommend {
  padding: 30rpx;
}

.recommend-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recommend-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFF8F5;
  border-radius: 15rpx;
}

.recommend-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
  background-color: #FFF8F5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dish-emoji {
  font-size: 60rpx;
}

.recommend-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.recommend-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.recommend-price {
  font-size: 28rpx;
}
