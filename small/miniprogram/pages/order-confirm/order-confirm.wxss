/* 订单确认页样式 */
.order-confirm-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 桌号信息 */
.table-info {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  padding: 30rpx;
  text-align: center;
}

.table-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* 订单商品列表 */
.order-items-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.items-list {
  padding: 0 30rpx;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-row:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-emoji {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.item-details {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-price {
  font-size: 24rpx;
  color: #666;
}

.item-quantity {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.quantity-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.subtotal-text {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
}

.items-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #fafafa;
  border-top: 1rpx solid #f0f0f0;
}

.summary-text {
  font-size: 28rpx;
  color: #666;
}

.summary-amount {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 优惠信息 */
.discount-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.discount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.discount-row:last-child {
  border-bottom: none;
}

.discount-label {
  display: flex;
  flex-direction: column;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.available-count, .balance-text {
  font-size: 24rpx;
  color: #FF6B35;
}

.discount-value {
  display: flex;
  align-items: center;
}

.value-text {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
  margin-right: 10rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 订单备注 */
.remark-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.remark-input {
  padding: 30rpx;
  font-size: 28rpx;
  min-height: 120rpx;
  background-color: #fafafa;
}

/* 价格明细 */
.price-detail {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row.final {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

.detail-value.discount {
  color: #FF6B35;
}

.detail-value.final-amount {
  font-size: 36rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 支付按钮 */
.pay-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.pay-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8A65 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pay-button.loading {
  background: #ccc;
}

.pay-button::after {
  border: none;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 优惠券列表 */
.coupon-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
}

.coupon-amount {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 返现弹窗 */
.cashback-content {
  padding: 30rpx;
}

.cashback-info {
  margin-bottom: 30rpx;
}

.info-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.cashback-input-row {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.cashback-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.input-unit {
  font-size: 28rpx;
  color: #666;
}

.confirm-button {
  width: 100%;
  height: 80rpx;
  background: #FF6B35;
  color: white;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
}

.confirm-button::after {
  border: none;
}
