// 订单确认页逻辑
interface OrderItem {
  id: number;
  name: string;
  description: string;
  price: number;
  emoji: string;
  image: string;
  categoryId: number;
  isRecommended?: boolean;
  count: number;
}

interface Coupon {
  id: number;
  name: string;
  amount: number;
  minAmount: number;
  expireDate: string;
}

Page({
  data: {
    tableNumber: '', // 桌号
    orderItems: [] as OrderItem[], // 订单商品
    totalCount: 0, // 商品总数量
    totalAmount: 0, // 商品总金额
    
    // 优惠信息
    selectedCoupon: null as Coupon | null, // 选中的优惠券
    discountAmount: 0, // 优惠金额
    cashbackUsed: 0, // 使用的返现金额
    finalAmount: 0, // 最终支付金额
    
    // 用户信息
    cashbackBalance: 128.50, // 返现余额
    availableCoupons: [] as Coupon[], // 可用优惠券
    
    // 界面状态
    showCouponModal: false, // 优惠券选择弹窗
    showCashbackModal: false, // 返现使用弹窗
    remark: '', // 订单备注
    
    // 支付状态
    isCreatingOrder: false, // 是否正在创建订单
    isPaymentReady: false // 是否准备支付
  },

  onLoad(options: any) {
    // 获取传递的订单数据
    const orderData = wx.getStorageSync('pendingOrder')
    if (!orderData) {
      wx.showToast({
        title: '订单数据异常',
        icon: 'error'
      })
      wx.navigateBack()
      return
    }

    // 设置订单数据
    this.setData({
      tableNumber: orderData.tableNumber,
      orderItems: orderData.items,
      totalCount: orderData.totalCount,
      totalAmount: orderData.totalPrice
    })

    // 计算最终金额
    this.calculateFinalAmount()
    
    // 加载用户优惠券
    this.loadAvailableCoupons()
  },

  // 计算最终支付金额
  calculateFinalAmount() {
    const { totalAmount, discountAmount, cashbackUsed } = this.data
    const finalAmount = Math.max(0, totalAmount - discountAmount - cashbackUsed)
    
    this.setData({
      finalAmount: finalAmount
    })
  },

  // 加载可用优惠券
  loadAvailableCoupons() {
    // 模拟优惠券数据，实际应该从API获取
    const coupons: Coupon[] = [
      {
        id: 1,
        name: '新用户专享',
        amount: 10,
        minAmount: 50,
        expireDate: '2024.12.31'
      },
      {
        id: 2,
        name: '会员专享',
        amount: 20,
        minAmount: 100,
        expireDate: '2024.12.31'
      }
    ]
    
    // 筛选可用的优惠券
    const { totalAmount } = this.data
    const availableCoupons = coupons.filter(coupon => totalAmount >= coupon.minAmount)
    
    this.setData({
      availableCoupons: availableCoupons
    })
  },

  // 显示优惠券选择
  showCouponSelector() {
    this.setData({
      showCouponModal: true
    })
  },

  // 隐藏优惠券弹窗
  hideCouponModal() {
    this.setData({
      showCouponModal: false
    })
  },

  // 选择优惠券
  selectCoupon(e: any) {
    const couponId = e.currentTarget.dataset.id
    const { availableCoupons } = this.data
    const selectedCoupon = availableCoupons.find(c => c.id === couponId)
    
    this.setData({
      selectedCoupon: selectedCoupon,
      discountAmount: selectedCoupon ? selectedCoupon.amount : 0,
      showCouponModal: false
    })
    
    this.calculateFinalAmount()
  },

  // 取消选择优惠券
  removeCoupon() {
    this.setData({
      selectedCoupon: null,
      discountAmount: 0
    })
    this.calculateFinalAmount()
  },

  // 显示返现使用弹窗
  showCashbackSelector() {
    this.setData({
      showCashbackModal: true
    })
  },

  // 隐藏返现弹窗
  hideCashbackModal() {
    this.setData({
      showCashbackModal: false
    })
  },

  // 返现金额输入
  onCashbackInput(e: any) {
    const inputAmount = parseFloat(e.detail.value) || 0
    const { cashbackBalance, totalAmount, discountAmount } = this.data
    
    // 限制返现使用金额
    const maxUsable = Math.min(cashbackBalance, totalAmount - discountAmount)
    const cashbackUsed = Math.min(inputAmount, maxUsable)
    
    this.setData({
      cashbackUsed: cashbackUsed
    })
    
    this.calculateFinalAmount()
  },

  // 确认使用返现
  confirmCashback() {
    this.setData({
      showCashbackModal: false
    })
  },

  // 订单备注输入
  onRemarkInput(e: any) {
    this.setData({
      remark: e.detail.value
    })
  },

  // 创建订单并支付
  async createOrderAndPay() {
    const { 
      tableNumber, 
      orderItems, 
      totalAmount, 
      selectedCoupon, 
      cashbackUsed, 
      finalAmount, 
      remark,
      isCreatingOrder 
    } = this.data

    if (isCreatingOrder) return

    this.setData({
      isCreatingOrder: true
    })

    try {
      // 创建订单数据
      const orderData = {
        tableNumber: tableNumber,
        items: orderItems,
        totalPrice: totalAmount,
        couponId: selectedCoupon?.id,
        cashbackUsed: cashbackUsed,
        remark: remark,
        createTime: new Date().getTime()
      }

      // TODO: 调用后端API创建订单
      // const response = await apiService.createOrder(orderData)
      
      // 模拟API响应
      const mockResponse = {
        success: true,
        data: {
          orderId: Date.now(),
          orderNumber: this.generateOrderNumber(),
          finalAmount: finalAmount,
          paymentInfo: {
            prepayId: 'wx_prepay_' + Date.now(),
            timeStamp: Math.floor(Date.now() / 1000).toString(),
            nonceStr: 'random_' + Math.random().toString(36).substr(2, 15),
            package: 'prepay_id=wx_prepay_' + Date.now(),
            signType: 'RSA',
            paySign: 'mock_sign_' + Date.now()
          }
        }
      }

      if (mockResponse.success) {
        // 保存订单到本地存储
        const orders = wx.getStorageSync('orders') || []
        const newOrder = {
          ...orderData,
          id: mockResponse.data.orderId,
          orderNumber: mockResponse.data.orderNumber,
          finalAmount: finalAmount,
          status: 'pending',
          paymentStatus: 0
        }
        orders.unshift(newOrder)
        wx.setStorageSync('orders', orders)

        // 调用微信支付
        this.callWechatPay(mockResponse.data.paymentInfo)
      } else {
        throw new Error('创建订单失败')
      }
    } catch (error) {
      console.error('创建订单失败:', error)
      wx.showToast({
        title: '创建订单失败',
        icon: 'error'
      })
    } finally {
      this.setData({
        isCreatingOrder: false
      })
    }
  },

  // 调用微信支付
  callWechatPay(paymentInfo: any) {
    wx.requestPayment({
      timeStamp: paymentInfo.timeStamp,
      nonceStr: paymentInfo.nonceStr,
      package: paymentInfo.package,
      signType: paymentInfo.signType,
      paySign: paymentInfo.paySign,
      success: (res) => {
        console.log('支付成功:', res)
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        })
        
        // 清除待处理订单数据
        wx.removeStorageSync('pendingOrder')
        
        // 跳转到个人页查看订单
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/profile/profile'
          })
        }, 1500)
      },
      fail: (err) => {
        console.error('支付失败:', err)
        wx.showToast({
          title: '支付失败',
          icon: 'error'
        })
      }
    })
  },

  // 生成订单号
  generateOrderNumber(): string {
    const now = new Date()
    const year = now.getFullYear().toString().slice(-2)
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const hour = now.getHours().toString().padStart(2, '0')
    const minute = now.getMinutes().toString().padStart(2, '0')
    const second = now.getSeconds().toString().padStart(2, '0')
    
    return `${year}${month}${day}${hour}${minute}${second}`
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  }
})
