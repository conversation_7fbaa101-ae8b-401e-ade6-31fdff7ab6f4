/* 彩色渐变底部导航栏组件样式 */

.gradient-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tabbar-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-top: 1rpx solid rgba(255, 255, 255, 0.1);
}

.tabbar-content {
  display: flex;
  height: 140rpx;
  align-items: center;
  justify-content: space-around;
  position: relative;
  z-index: 2;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  position: relative;
  border-radius: 32rpx;
  margin: 16rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20rpx);
}

.tab-icon {
  width: 52rpx;
  height: 52rpx;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.icon-image {
  width: 100%;
  height: 100%;
}

.tab-text {
  font-size: 22rpx;
  line-height: 1;
  font-weight: 500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.active-indicator {
  position: absolute;
  top: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  border-radius: 3rpx;
  opacity: 0.8;
}

/* 经典蓝紫渐变主题 */
.gradient-tabbar.classic .tabbar-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  box-shadow: 0 -16rpx 64rpx rgba(102, 126, 234, 0.4);
}

.gradient-tabbar.classic .tab-item {
  color: rgba(255, 255, 255, 0.7);
}

.gradient-tabbar.classic .tab-item.active {
  color: #fff;
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-6rpx);
  box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.15);
  animation: pulse 2s ease-in-out infinite;
}

.gradient-tabbar.classic .tab-item:not(.active):active {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2rpx);
}

.gradient-tabbar.classic .tab-item.active .tab-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.2));
}

.gradient-tabbar.classic .tab-item.active .tab-text {
  font-weight: 600;
}

.gradient-tabbar.classic .active-indicator {
  background: linear-gradient(90deg, transparent, #fff, transparent);
}

/* 彩虹渐变主题 */
.gradient-tabbar.rainbow .tabbar-background {
  background: linear-gradient(45deg, 
    #ff6b6b 0%, 
    #4ecdc4 20%, 
    #45b7d1 40%, 
    #96ceb4 60%, 
    #feca57 80%, 
    #ff9ff3 100%);
  background-size: 300% 300%;
  animation: rainbowShift 6s ease infinite;
  box-shadow: 0 -20rpx 80rpx rgba(0, 0, 0, 0.2);
}

.gradient-tabbar.rainbow .tab-item {
  color: rgba(255, 255, 255, 0.8);
}

.gradient-tabbar.rainbow .tab-item.active {
  color: #fff;
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-10rpx) scale(1.05);
  box-shadow: 0 30rpx 70rpx rgba(0, 0, 0, 0.2);
}

.gradient-tabbar.rainbow .tab-item.active .tab-icon {
  transform: scale(1.2) rotate(5deg);
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.3));
}

.gradient-tabbar.rainbow .tab-item.active .tab-text {
  font-weight: 600;
  font-size: 24rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.gradient-tabbar.rainbow .active-indicator {
  background: linear-gradient(90deg, 
    transparent, 
    #fff 20%, 
    #ff6b6b 40%, 
    #4ecdc4 60%, 
    #fff 80%, 
    transparent);
  animation: glow 2s ease infinite;
}

/* 深色主题 */
.gradient-tabbar.dark .tabbar-background {
  background-color: #1c1c1e;
  border-top: 1rpx solid #38383a;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.3);
}

.gradient-tabbar.dark .tab-item {
  color: #8e8e93;
}

.gradient-tabbar.dark .tab-item.active {
  color: #0a84ff;
  background: rgba(10, 132, 255, 0.1);
  transform: translateY(-4rpx);
}

.gradient-tabbar.dark .tab-item.active .tab-icon {
  transform: scale(1.1);
}

.gradient-tabbar.dark .tab-item.active .tab-text {
  font-weight: 600;
}

.gradient-tabbar.dark .active-indicator {
  background-color: #0a84ff;
}

/* 毛玻璃主题 */
.gradient-tabbar.glass .tabbar-background {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(40rpx);
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 -16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.gradient-tabbar.glass .tab-item {
  color: #666;
}

.gradient-tabbar.glass .tab-item.active {
  color: #FF6B35; /* 使用小程序主题色 */
  background: rgba(255, 107, 53, 0.1);
  transform: translateY(-4rpx);
}

.gradient-tabbar.glass .tab-item.active .tab-icon {
  transform: scale(1.1);
}

.gradient-tabbar.glass .tab-item.active .tab-text {
  font-weight: 600;
}

.gradient-tabbar.glass .active-indicator {
  background-color: #FF6B35;
}

/* 动画效果 */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes rainbowShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes pulse {
  0% { box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.15); }
  50% { box-shadow: 0 16rpx 70rpx rgba(255, 255, 255, 0.2); }
  100% { box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.15); }
}

@keyframes glow {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 0.6; }
}
