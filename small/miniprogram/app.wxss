/**app.wxss**/
/* 全局样式 */
page {
  background-color: #F5F5F5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 主色调 */
.primary-color { color: #FF6B35; }
.primary-bg { background-color: #FF6B35; }
.secondary-color { color: #F7931E; }
.member-color { color: #FFD700; }
.success-color { color: #4CAF50; }
.text-gray { color: #999999; }
.text-dark { color: #333333; }

/* 通用容器 */
.container {
  padding: 20rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

/* 按钮样式 */
.btn {
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.btn-primary {
  background-color: #FF6B35;
  color: #ffffff;
}

.btn-primary:active {
  background-color: #E55A2B;
}

.btn-secondary {
  background-color: #F7931E;
  color: #ffffff;
}

/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

/* 列表项 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
}

.list-item:last-child {
  border-bottom: none;
}

/* 文本样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

.price {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B35;
}

/* 徽章 */
.badge {
  background-color: #FF6B35;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  position: absolute;
  top: -10rpx;
  right: -10rpx;
}
